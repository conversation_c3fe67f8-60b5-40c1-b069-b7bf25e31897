#include "../include/protocol.h"
#include "../include/board.h"
#include "../include/ai_engine.h"

// 处理移动命令
void handleMoveCommand() {
    Step step;
    char message[256];
    
    scanf("%s", message);
    fflush(stdin);

    // 解析对手着法 - 修正坐标系统
    // 第一个字符是列(A-F)，第二个字符是行(A-F)
    step.start.y = message[0] - 'A';  // 列坐标
    step.start.x = message[1] - 'A';  // 行坐标
    step.end.y = message[2] - 'A';    // 列坐标
    step.end.x = message[3] - 'A';    // 行坐标

    // 验证对手移动的合法性（基本检查）
    if (step.start.x >= 0 && step.start.x < BOARD_SIZE &&
        step.start.y >= 0 && step.start.y < BOARD_SIZE &&
        step.end.x >= 0 && step.end.x < BOARD_SIZE &&
        step.end.y >= 0 && step.end.y < BOARD_SIZE &&
        Board[step.start.x][step.start.y] == (computerSide ^ 1)) {

        // 处理对手行棋
        Board[step.end.x][step.end.y] = Board[step.start.x][step.start.y];
        Board[step.start.x][step.start.y] = EMPTY;
    }

    // 生成己方着法
    step = generateBestMove();

    // 处理己方行棋
    if (step.start.x != -1) {
        Board[step.end.x][step.end.y] = Board[step.start.x][step.start.y];
        Board[step.start.x][step.start.y] = EMPTY;

        // 输出着法 - 修正坐标系统
        cout << "move " << (char)(step.start.y + 'A') << (char)(step.start.x + 'A')
             << (char)(step.end.y + 'A') << (char)(step.end.x + 'A') << endl;
    }
}

// 处理新游戏命令
void handleNewCommand() {
    Step step;
    char message[256];
    
    scanf("%s", message);
    fflush(stdin);

    if (strcmp(message, "black") == 0) {
        computerSide = BLACK;
    } else {
        computerSide = WHITE;
    }

    // 初始化棋局
    initializeBoard();
    gameStarted = 1;

    if (computerSide == BLACK) {
        // 黑方先手，生成第一手着法
        step = generateBestMove();

        if (step.start.x != -1) {
            // 处理己方行棋
            Board[step.end.x][step.end.y] = Board[step.start.x][step.start.y];
            Board[step.start.x][step.start.y] = EMPTY;

            // 输出着法 - 修正坐标系统
            cout << "move " << (char)(step.start.y + 'A') << (char)(step.start.x + 'A')
                 << (char)(step.end.y + 'A') << (char)(step.end.x + 'A') << endl;
        }
    }
}

// 处理错误命令
void handleErrorCommand() {
    Step step;
    fflush(stdin);
    // 重新生成着法
    step = generateBestMove();
    if (step.start.x != -1) {
        cout << "move " << (char)(step.start.y + 'A') << (char)(step.start.x + 'A')
             << (char)(step.end.y + 'A') << (char)(step.end.x + 'A') << endl;
    }
}

// 处理名称查询
void handleNameQuery() {
    fflush(stdin);
    cout << "name SurakartaEngine" << endl;
}

// 处理游戏结束命令
void handleEndCommand() {
    fflush(stdin);
    gameStarted = 0;
}

// 处理退出命令
void handleQuitCommand() {
    fflush(stdin);
    cout << "Quit!" << endl;
}

// 主通信循环
int runProtocolLoop() {
    char message[256];

    // 初始化随机数种子
    srand((unsigned int)time(NULL));

    // 程序主循环
    while (1) {
        fflush(stdout);

        // 获取平台消息
        if (scanf("%s", message) != 1) {
            break; // 输入结束或出错时退出
        }

        // 分析命令
        if (strcmp(message, "move") == 0) {
            handleMoveCommand();
        }
        else if (strcmp(message, "new") == 0) {
            handleNewCommand();
        }
        else if (strcmp(message, "error") == 0) {
            handleErrorCommand();
        }
        else if (strcmp(message, "name?") == 0) {
            handleNameQuery();
        }
        else if (strcmp(message, "end") == 0) {
            handleEndCommand();
        }
        else if (strcmp(message, "quit") == 0) {
            handleQuitCommand();
            break;
        }
    }

    return 0;
}
