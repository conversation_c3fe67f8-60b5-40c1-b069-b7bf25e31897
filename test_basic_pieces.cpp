#include "include/common.h"
#include "include/board.h"

// 基础棋子测试
int main() {
    cout << "=== 基础棋子测试 ===" << endl;
    
    computerSide = BLACK;
    initializeBoard();
    
    cout << "初始棋盘:" << endl;
    printBoard();
    
    cout << "computerSide = " << computerSide << endl;
    cout << "BLACK = " << BLACK << ", WHITE = " << WHITE << ", EMPTY = " << EMPTY << endl;
    
    // 手动扫描棋盘
    cout << "\n手动扫描棋盘:" << endl;
    for (int i = 0; i < BOARD_SIZE; i++) {
        for (int j = 0; j < BOARD_SIZE; j++) {
            if (Board[i][j] == computerSide) {
                cout << "找到己方棋子在: (" << i << "," << j << ")" << endl;
            }
        }
    }
    
    cout << "\n测试完成" << endl;
    return 0;
}
