#include "include/common.h"
#include "include/board.h"

// 简单移动测试，不涉及弧线吃子
int main() {
    cout << "=== 简单移动测试 ===" << endl;
    
    computerSide = BLACK;
    
    // 手动设置一个简单的棋盘状态
    for (int i = 0; i < BOARD_SIZE; i++) {
        for (int j = 0; j < BOARD_SIZE; j++) {
            Board[i][j] = EMPTY;
        }
    }
    
    // 只放一个黑子
    Board[2][2] = BLACK;
    
    cout << "简单棋盘状态:" << endl;
    printBoard();
    
    cout << "computerSide = " << computerSide << endl;
    
    // 手动扫描找到己方棋子
    cout << "\n扫描己方棋子:" << endl;
    for (int i = 0; i < BOARD_SIZE; i++) {
        for (int j = 0; j < BOARD_SIZE; j++) {
            if (Board[i][j] == computerSide) {
                cout << "找到己方棋子在: (" << i << "," << j << ")" << endl;
                
                // 检查可能的移动
                cout << "检查可能的移动:" << endl;
                for (int di = -1; di <= 1; di++) {
                    for (int dj = -1; dj <= 1; dj++) {
                        if (di == 0 && dj == 0) continue;
                        
                        int newX = i + di;
                        int newY = j + dj;
                        
                        if (newX >= 0 && newX < BOARD_SIZE && 
                            newY >= 0 && newY < BOARD_SIZE) {
                            
                            if (Board[newX][newY] == EMPTY) {
                                cout << "  可以移动到: (" << newX << "," << newY << ")" << endl;
                            }
                        }
                    }
                }
            }
        }
    }
    
    cout << "\n测试完成" << endl;
    return 0;
}
