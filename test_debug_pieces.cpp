#include "include/common.h"
#include "include/board.h"
#include "include/ai_engine.h"
#include "include/protocol.h"

// 详细调试棋子移动问题
int main() {
    cout << "=== 详细调试棋子移动问题 ===" << endl;
    
    // 测试1: 初始状态
    cout << "\n--- 测试1: 初始状态 ---" << endl;
    computerSide = BLACK;
    initializeBoard();
    
    cout << "初始棋盘状态:" << endl;
    printBoard();
    
    cout << "computerSide = " << computerSide << " (BLACK=" << BLACK << ", WHITE=" << WHITE << ")" << endl;
    
    // 手动验证棋盘状态
    cout << "\n手动验证棋盘状态:" << endl;
    int blackCount = 0, whiteCount = 0, emptyCount = 0;
    for (int i = 0; i < BOARD_SIZE; i++) {
        for (int j = 0; j < BOARD_SIZE; j++) {
            if (Board[i][j] == BLACK) blackCount++;
            else if (Board[i][j] == WHITE) whiteCount++;
            else if (Board[i][j] == EMPTY) emptyCount++;
            else {
                cout << "错误：位置(" << i << "," << j << ")有无效棋子值: " << Board[i][j] << endl;
            }
        }
    }
    cout << "黑子数量: " << blackCount << ", 白子数量: " << whiteCount << ", 空位数量: " << emptyCount << endl;
    
    // 生成移动
    cout << "\n生成己方移动..." << endl;
    Step move = generateBestMove();
    
    if (move.start.x != -1) {
        cout << "\n=== 详细验证生成的移动 ===" << endl;
        cout << "移动: (" << move.start.x << "," << move.start.y 
             << ") -> (" << move.end.x << "," << move.end.y << ")" << endl;
        
        // 验证起始位置
        cout << "起始位置验证:" << endl;
        cout << "  坐标: (" << move.start.x << "," << move.start.y << ")" << endl;
        cout << "  棋子值: " << Board[move.start.x][move.start.y] << endl;
        cout << "  computerSide: " << computerSide << endl;
        cout << "  是否匹配: " << (Board[move.start.x][move.start.y] == computerSide ? "是" : "否") << endl;
        
        if (Board[move.start.x][move.start.y] != computerSide) {
            cout << "✗ 严重错误：尝试移动不是己方的棋子！" << endl;
            if (Board[move.start.x][move.start.y] == EMPTY) {
                cout << "  起始位置是空的！" << endl;
            } else if (Board[move.start.x][move.start.y] == (computerSide ^ 1)) {
                cout << "  起始位置是对方棋子！" << endl;
            }
        } else {
            cout << "✓ 起始位置验证通过" << endl;
        }
        
        // 验证目标位置
        cout << "\n目标位置验证:" << endl;
        cout << "  坐标: (" << move.end.x << "," << move.end.y << ")" << endl;
        cout << "  棋子值: " << Board[move.end.x][move.end.y] << endl;
        
        if (Board[move.end.x][move.end.y] == EMPTY) {
            cout << "  目标位置为空（普通移动）" << endl;
        } else if (Board[move.end.x][move.end.y] == (computerSide ^ 1)) {
            cout << "  目标位置有对方棋子（吃子移动）" << endl;
        } else if (Board[move.end.x][move.end.y] == computerSide) {
            cout << "✗ 错误：目标位置有己方棋子！" << endl;
        }
        
        // 尝试执行移动
        cout << "\n尝试执行移动..." << endl;
        if (executeSafeMove(move)) {
            cout << "✓ 移动执行成功" << endl;
            cout << "执行后棋盘:" << endl;
            printBoard();
        } else {
            cout << "✗ 移动执行失败" << endl;
        }
    } else {
        cout << "无法生成移动" << endl;
    }
    
    // 测试2: 模拟对方移动后的状态
    cout << "\n--- 测试2: 模拟对方移动 ---" << endl;
    
    // 重新初始化
    initializeBoard();
    computerSide = BLACK;
    
    // 模拟对方（白方）移动一个棋子
    cout << "模拟白方移动: (4,0) -> (3,0)" << endl;
    Board[3][0] = WHITE;  // 白子移动到(3,0)
    Board[4][0] = EMPTY;  // 原位置变空
    
    cout << "模拟移动后棋盘:" << endl;
    printBoard();
    
    // 现在测试黑方的反应
    cout << "现在测试黑方的移动..." << endl;
    Step move2 = generateBestMove();
    
    if (move2.start.x != -1) {
        cout << "黑方生成移动: (" << move2.start.x << "," << move2.start.y 
             << ") -> (" << move2.end.x << "," << move2.end.y << ")" << endl;
        
        // 验证这个移动
        if (Board[move2.start.x][move2.start.y] == BLACK) {
            cout << "✓ 起始位置有黑子" << endl;
        } else {
            cout << "✗ 错误：起始位置没有黑子！实际值: " << Board[move2.start.x][move2.start.y] << endl;
        }
    }
    
    cout << "\n=== 调试测试完成 ===" << endl;
    return 0;
}
