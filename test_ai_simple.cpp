#include "include/common.h"
#include "include/board.h"
#include "include/ai_engine.h"

// 测试AI生成移动（不涉及弧线吃子）
int main() {
    cout << "=== 测试AI生成移动（简化版）===" << endl;
    
    computerSide = BLACK;
    
    // 手动设置一个简单的棋盘状态
    for (int i = 0; i < BOARD_SIZE; i++) {
        for (int j = 0; j < BOARD_SIZE; j++) {
            Board[i][j] = EMPTY;
        }
    }
    
    // 只放一个黑子
    Board[2][2] = BLACK;
    
    cout << "简单棋盘状态:" << endl;
    printBoard();
    
    cout << "computerSide = " << computerSide << endl;
    
    // 生成移动
    cout << "\n生成AI移动..." << endl;
    Step move = generateBestMove();
    
    if (move.start.x != -1) {
        cout << "AI生成移动: (" << move.start.x << "," << move.start.y 
             << ") -> (" << move.end.x << "," << move.end.y << ")" << endl;
        
        // 验证移动
        cout << "验证移动:" << endl;
        cout << "  起始位置棋子: " << Board[move.start.x][move.start.y] << endl;
        cout << "  目标位置棋子: " << Board[move.end.x][move.end.y] << endl;
        cout << "  computerSide: " << computerSide << endl;
        
        if (Board[move.start.x][move.start.y] == computerSide) {
            cout << "✓ 起始位置验证通过" << endl;
        } else {
            cout << "✗ 错误：起始位置验证失败！" << endl;
        }
        
        if (Board[move.end.x][move.end.y] == EMPTY) {
            cout << "✓ 目标位置为空" << endl;
        } else {
            cout << "✗ 错误：目标位置不为空！" << endl;
        }
    } else {
        cout << "AI无法生成移动" << endl;
    }
    
    cout << "\n测试完成" << endl;
    return 0;
}
