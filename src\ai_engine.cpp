#include "../include/ai_engine.h"
#include "../include/board.h"
#include "../include/arc_capture.h"

// 打印弧线吃子分析（调试用）
void printArcCaptureAnalysis() {
    cout << "=== 弧线吃子分析 ===" << endl;

    // 分析当前方的弧线吃子机会
    cout << "当前方 (" << (computerSide == BLACK ? "黑" : "白") << ") 的弧线吃子机会:" << endl;

    int captureCount = 0;
    for (int i = 0; i < BOARD_SIZE; i++) {
        for (int j = 0; j < BOARD_SIZE; j++) {
            if (Board[i][j] == computerSide) {
                for (int ti = 0; ti < BOARD_SIZE; ti++) {
                    for (int tj = 0; tj < BOARD_SIZE; tj++) {
                        if (Board[ti][tj] == (computerSide ^ 1)) {
                            if (isValidArcCapture(i, j, ti, tj)) {
                                cout << "  可以从 " << (char)('A' + j) << (char)('A' + i)
                                     << " 吃掉 " << (char)('A' + tj) << (char)('A' + ti) << endl;
                                captureCount++;
                            }
                        }
                    }
                }
            }
        }
    }

    if (captureCount == 0) {
        cout << "  没有可用的弧线吃子机会" << endl;
    }

    cout << "总计: " << captureCount << " 个弧线吃子机会" << endl;
    cout << "===================" << endl;
}

// 改进的位置评估函数
int evaluatePosition() {
    int myPieces = countPieces(computerSide);
    int opponentPieces = countPieces(computerSide ^ 1);

    int score = 0;

    // 基础评估：己方棋子数 - 对方棋子数
    score += (myPieces - opponentPieces) * 100;

    // 位置评估：中心位置更有价值
    for (int i = 0; i < BOARD_SIZE; i++) {
        for (int j = 0; j < BOARD_SIZE; j++) {
            if (Board[i][j] == computerSide) {
                // 中心位置加分
                if (i >= 2 && i <= 3 && j >= 2 && j <= 3) {
                    score += 10;
                }
                // 边缘位置稍微减分
                if (i == 0 || i == BOARD_SIZE-1 || j == 0 || j == BOARD_SIZE-1) {
                    score -= 5;
                }
            } else if (Board[i][j] == (computerSide ^ 1)) {
                // 对方在中心位置减分
                if (i >= 2 && i <= 3 && j >= 2 && j <= 3) {
                    score -= 10;
                }
            }
        }
    }

    return score;
}

// 完全重写的最佳移动生成算法 - 不修改原棋盘
Step generateBestMove() {
    Step bestMove(Point(-1, -1), Point(-1, -1), -10000);
    vector<Step> allValidMoves;

    // 遍历所有己方棋子
    for (int i = 0; i < BOARD_SIZE; i++) {
        for (int j = 0; j < BOARD_SIZE; j++) {
            // 严格检查：确保这个位置有己方棋子
            if (Board[i][j] != computerSide) {
                continue;
            }

            Point start(i, j);

            // 检查所有可能的目标位置
            for (int ti = 0; ti < BOARD_SIZE; ti++) {
                for (int tj = 0; tj < BOARD_SIZE; tj++) {
                    Point end(ti, tj);

                    // 跳过相同位置
                    if (i == ti && j == tj) {
                        continue;
                    }

                    // 创建临时棋盘进行模拟
                    int tempBoard[BOARD_SIZE][BOARD_SIZE];
                    copyBoard(Board, tempBoard);

                    // 临时设置computerSide用于验证
                    int originalSide = computerSide;

                    // 检查是否是合法的吃子移动
                    if (Board[ti][tj] == (computerSide ^ 1) && canCapture(start, end)) {
                        // 模拟吃子移动
                        tempBoard[ti][tj] = tempBoard[i][j];
                        tempBoard[i][j] = EMPTY;

                        // 计算评估值（不修改原棋盘）
                        int value = evaluatePositionOnBoard(tempBoard) + 300;
                        Step captureMove(start, end, value);
                        allValidMoves.push_back(captureMove);
                    }
                    // 检查是否是合法的普通移动
                    else if (Board[ti][tj] == EMPTY && isValidMove(start, end)) {
                        // 模拟普通移动
                        tempBoard[ti][tj] = tempBoard[i][j];
                        tempBoard[i][j] = EMPTY;

                        // 计算评估值（不修改原棋盘）
                        int value = evaluatePositionOnBoard(tempBoard);
                        Step normalMove(start, end, value);
                        allValidMoves.push_back(normalMove);
                    }

                    // 恢复computerSide
                    computerSide = originalSide;
                }
            }
        }
    }

    // 从所有合法移动中选择最佳的
    for (const Step& move : allValidMoves) {
        if (move.value > bestMove.value) {
            bestMove = move;
        }
    }

    // 如果没有找到任何移动，尝试找一个简单的合法移动
    if (bestMove.start.x == -1) {
        bestMove = findAnyValidMove();
    }

    return bestMove;
}

// 在指定棋盘上进行位置评估（不修改全局棋盘）
int evaluatePositionOnBoard(int board[BOARD_SIZE][BOARD_SIZE]) {
    int myPieces = 0;
    int opponentPieces = 0;
    int score = 0;

    // 计算棋子数量和位置评估
    for (int i = 0; i < BOARD_SIZE; i++) {
        for (int j = 0; j < BOARD_SIZE; j++) {
            if (board[i][j] == computerSide) {
                myPieces++;
                // 中心位置加分
                if (i >= 2 && i <= 3 && j >= 2 && j <= 3) {
                    score += 10;
                }
                // 边缘位置稍微减分
                if (i == 0 || i == BOARD_SIZE-1 || j == 0 || j == BOARD_SIZE-1) {
                    score -= 5;
                }
            } else if (board[i][j] == (computerSide ^ 1)) {
                opponentPieces++;
                // 对方在中心位置减分
                if (i >= 2 && i <= 3 && j >= 2 && j <= 3) {
                    score -= 10;
                }
            }
        }
    }

    // 基础评估：己方棋子数 - 对方棋子数
    score += (myPieces - opponentPieces) * 100;

    return score;
}

// 完全重写的安全移动查找函数
Step findAnyValidMove() {
    // 第一优先级：寻找普通移动（8方向，一格）
    for (int i = 0; i < BOARD_SIZE; i++) {
        for (int j = 0; j < BOARD_SIZE; j++) {
            // 严格检查：确保这个位置有己方棋子
            if (Board[i][j] != computerSide) {
                continue;
            }

            Point start(i, j);

            // 尝试8个方向的移动
            for (int di = -1; di <= 1; di++) {
                for (int dj = -1; dj <= 1; dj++) {
                    if (di == 0 && dj == 0) continue;

                    int newX = i + di;
                    int newY = j + dj;

                    // 检查边界
                    if (newX >= 0 && newX < BOARD_SIZE &&
                        newY >= 0 && newY < BOARD_SIZE) {

                        Point end(newX, newY);

                        // 严格检查：目标位置必须为空
                        if (Board[newX][newY] == EMPTY) {
                            // 双重验证移动的合法性
                            if (isValidMove(start, end)) {
                                return Step(start, end, 0);
                            }
                        }
                    }
                }
            }
        }
    }

    // 第二优先级：寻找吃子移动
    for (int i = 0; i < BOARD_SIZE; i++) {
        for (int j = 0; j < BOARD_SIZE; j++) {
            // 严格检查：确保这个位置有己方棋子
            if (Board[i][j] != computerSide) {
                continue;
            }

            Point start(i, j);

            // 寻找可以吃掉的对方棋子
            for (int ti = 0; ti < BOARD_SIZE; ti++) {
                for (int tj = 0; tj < BOARD_SIZE; tj++) {
                    // 严格检查：目标位置必须有对方棋子
                    if (Board[ti][tj] != (computerSide ^ 1)) {
                        continue;
                    }

                    Point end(ti, tj);

                    // 双重验证吃子的合法性
                    if (canCapture(start, end)) {
                        return Step(start, end, 100);
                    }
                }
            }
        }
    }

    // 如果真的找不到任何移动，返回无效移动
    return Step(Point(-1, -1), Point(-1, -1), -1);
}
