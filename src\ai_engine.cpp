#include "../include/ai_engine.h"
#include "../include/board.h"
#include "../include/arc_capture.h"

// 打印弧线吃子分析（调试用）
void printArcCaptureAnalysis() {
    cout << "=== 弧线吃子分析 ===" << endl;

    // 分析当前方的弧线吃子机会
    cout << "当前方 (" << (computerSide == BLACK ? "黑" : "白") << ") 的弧线吃子机会:" << endl;

    int captureCount = 0;
    for (int i = 0; i < BOARD_SIZE; i++) {
        for (int j = 0; j < BOARD_SIZE; j++) {
            if (Board[i][j] == computerSide) {
                for (int ti = 0; ti < BOARD_SIZE; ti++) {
                    for (int tj = 0; tj < BOARD_SIZE; tj++) {
                        if (Board[ti][tj] == (computerSide ^ 1)) {
                            if (isValidArcCapture(i, j, ti, tj)) {
                                cout << "  可以从 " << (char)('A' + j) << (char)('A' + i)
                                     << " 吃掉 " << (char)('A' + tj) << (char)('A' + ti) << endl;
                                captureCount++;
                            }
                        }
                    }
                }
            }
        }
    }

    if (captureCount == 0) {
        cout << "  没有可用的弧线吃子机会" << endl;
    }

    cout << "总计: " << captureCount << " 个弧线吃子机会" << endl;
    cout << "===================" << endl;
}

// 改进的位置评估函数
int evaluatePosition() {
    int myPieces = countPieces(computerSide);
    int opponentPieces = countPieces(computerSide ^ 1);

    int score = 0;

    // 基础评估：己方棋子数 - 对方棋子数
    score += (myPieces - opponentPieces) * 100;

    // 位置评估：中心位置更有价值
    for (int i = 0; i < BOARD_SIZE; i++) {
        for (int j = 0; j < BOARD_SIZE; j++) {
            if (Board[i][j] == computerSide) {
                // 中心位置加分
                if (i >= 2 && i <= 3 && j >= 2 && j <= 3) {
                    score += 10;
                }
                // 边缘位置稍微减分
                if (i == 0 || i == BOARD_SIZE-1 || j == 0 || j == BOARD_SIZE-1) {
                    score -= 5;
                }
            } else if (Board[i][j] == (computerSide ^ 1)) {
                // 对方在中心位置减分
                if (i >= 2 && i <= 3 && j >= 2 && j <= 3) {
                    score -= 10;
                }
            }
        }
    }

    return score;
}

// 改进的最佳移动生成算法
Step generateBestMove() {
    Step bestMove(Point(-1, -1), Point(-1, -1), -10000);
    vector<Step> captureMoves;
    vector<Step> normalMoves;

    // 遍历所有己方棋子
    for (int i = 0; i < BOARD_SIZE; i++) {
        for (int j = 0; j < BOARD_SIZE; j++) {
            if (Board[i][j] == computerSide) {
                Point start(i, j);

                // 首先收集所有弧线吃子移动（优先级最高）
                for (int ti = 0; ti < BOARD_SIZE; ti++) {
                    for (int tj = 0; tj < BOARD_SIZE; tj++) {
                        Point end(ti, tj);

                        if (canCapture(start, end)) {
                            // 尝试吃子并评估
                            int tempBoard[BOARD_SIZE][BOARD_SIZE];
                            copyBoard(Board, tempBoard);

                            makeMove(start, end);
                            int value = evaluatePosition() + 300; // 弧线吃子超高奖励

                            Step captureMove(start, end, value);
                            captureMoves.push_back(captureMove);

                            // 恢复棋盘
                            copyBoard(tempBoard, Board);
                        }
                    }
                }

                // 然后收集普通移动
                for (int di = -1; di <= 1; di++) {
                    for (int dj = -1; dj <= 1; dj++) {
                        if (di == 0 && dj == 0) continue;

                        Point end(i + di, j + dj);

                        if (isValidMove(start, end)) {
                            // 尝试移动并评估
                            int tempBoard[BOARD_SIZE][BOARD_SIZE];
                            copyBoard(Board, tempBoard);

                            makeMove(start, end);
                            int value = evaluatePosition();

                            Step normalMove(start, end, value);
                            normalMoves.push_back(normalMove);

                            // 恢复棋盘
                            copyBoard(tempBoard, Board);
                        }
                    }
                }
            }
        }
    }

    // 优先选择吃子移动
    if (!captureMoves.empty()) {
        for (const Step& move : captureMoves) {
            if (move.value > bestMove.value) {
                bestMove = move;
            }
        }
    } else if (!normalMoves.empty()) {
        // 如果没有吃子移动，选择最佳普通移动
        for (const Step& move : normalMoves) {
            if (move.value > bestMove.value) {
                bestMove = move;
            }
        }
    }

    return bestMove;
}
